# API密钥智能调用控制功能说明

## 🎯 功能概述

已成功为AI命名工具添加了智能API密钥调用控制功能，实现了以下核心特性：

### ✅ 核心功能

1. **调用频率控制**
   - 每个API密钥调用4次后自动休息1分钟（60秒）
   - 防止API密钥被过度使用，避免触发限流

2. **智能密钥轮换**
   - 自动在多个API密钥之间轮换使用
   - 当某个密钥进入休息期时，自动切换到其他可用密钥
   - 当所有密钥都在休息时，程序会等待最短的休息时间

3. **动态调整机制**
   - 根据API调用成功率自动调整调用频率
   - 成功率高时（>95%）：增加调用频率，减少休息时间
   - 成功率低时（<80%）：降低调用频率，增加休息时间

4. **详细状态监控**
   - 实时显示每个API密钥的使用状态
   - 显示当前周期调用次数和剩余休息时间
   - 统计总体成功率和调用分布

## 🔧 技术实现

### 新增变量
```python
# API密钥调用控制
self.key_call_count = {}      # 记录每个密钥当前周期的调用次数
self.key_rest_until = {}      # 记录每个密钥的休息结束时间
self.max_calls_per_cycle = 4  # 每个周期最大调用次数
self.rest_duration = 60       # 休息时间（秒）

# 动态调整相关
self.key_error_count = {}     # 记录每个密钥的错误次数
self.key_success_count = {}   # 记录每个密钥的成功次数
self.adaptive_mode = True     # 是否启用自适应调整
```

### 核心方法

1. **`get_next_key()`** - 智能密钥选择
   - 检查密钥休息状态
   - 自动轮换到可用密钥
   - 处理所有密钥都在休息的情况

2. **`_adjust_call_frequency()`** - 动态频率调整
   - 根据成功率自动优化参数
   - 每30秒检查一次调整需求

3. **`_record_api_result()`** - 结果记录
   - 记录每次API调用的成功/失败状态
   - 为动态调整提供数据支持

## 📊 控制台输出改进

### 实时状态显示
- ⏸ API密钥休息提醒
- ⏳ 等待时间显示
- ✓ 成功调用信息（包含使用的密钥）
- ✗ 失败调用详情
- 📊 定期API状态报告
- 📈/📉 动态调整通知

### 统计信息增强
- 显示每个密钥的详细状态
- 总体成功率统计
- 调用控制参数显示
- 自适应调整状态

## 🎮 使用体验

### 启动时显示
```
> 调用控制: 每个API密钥调用4次后休息60秒
▶ 开始处理，使用 6 个线程，3 个API密钥
> 智能调用控制已启用，将自动管理API密钥使用频率
```

### 处理过程中
```
⏸ API密钥 sk-abc123...def456 已调用4次，休息60秒
✓ 已处理: image1.jpg -> 2D Flat Rotating Acrylic Desktop... (密钥: sk-xyz789...uvw012)
📊 API状态: 1/3 个密钥休息中
⏳ 所有API密钥都在休息中，等待 15.3 秒...
📈 成功率高(96.5%)，调整为每5次调用休息50秒
```

### 完成后统计
```
▌ API密钥使用统计 ▐
└─ 密钥 sk-abc123...def456 (用户): 25 次调用 (33.3%) [当前周期: 1/4]
└─ 密钥 sk-xyz789...uvw012 (用户): 24 次调用 (32.0%) [休息中: 45.2s]
└─ 密钥 sk-mno345...pqr678 (用户): 26 次调用 (34.7%) [当前周期: 2/4]
└─ 总调用次数: 75
└─ 调用控制: 每4次调用休息60秒
└─ 总体成功率: 97.3% (73/75)
└─ 自适应调整: 已启用 (根据成功率自动优化调用频率)
```

## 🚀 优势特点

1. **智能化管理** - 无需手动干预，自动管理API密钥使用
2. **高效利用** - 最大化API密钥的使用效率
3. **稳定可靠** - 避免因过度调用导致的限流问题
4. **自适应优化** - 根据实际情况动态调整策略
5. **详细监控** - 提供全面的状态信息和统计数据
6. **用户友好** - 清晰的控制台输出，易于理解当前状态

## 📝 配置说明

可以通过修改以下参数来调整控制策略：

- `max_calls_per_cycle`: 每个周期最大调用次数（默认4次）
- `rest_duration`: 休息时间（默认60秒）
- `adaptive_mode`: 是否启用自适应调整（默认True）

这些参数会根据API响应情况自动优化，也可以根据需要手动调整。
